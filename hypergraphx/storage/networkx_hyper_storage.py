import os
from dataclasses import dataclass
from typing import Optional, Union, Iterable

import networkx as nx

try:
    # Reuse GraphGen's logger if available
    from graphgen.utils import logger
except Exception:  # pragma: no cover
    import logging

    logger = logging.getLogger("hypergraphx")
    if not logger.handlers:
        logger.addHandler(logging.StreamHandler())
    logger.setLevel(logging.INFO)


@dataclass
class NetworkXHyperStorage:
    """
    A NetworkX-based persistent storage for hypergraph-style knowledge graphs.

    Semantics:
    - Use a directed graph (DiGraph).
    - Represent a hyperedge as a node with role="hyperedge" and attributes like
      weight (float) and source_id (str).
    - Connect hyperedge -> entity via a directed edge with attributes like
      weight (float) and source_id (str).

    This class is compatible with the GraphStorage protocol defined in
    hypergraph.types (methods/signatures match), so it can be used wherever a
    GraphStorage is expected.
    """

    working_dir: str
    namespace: str = "hypergraph"

    def __post_init__(self):
        os.makedirs(self.working_dir, exist_ok=True)
        self._graphml_xml_file = os.path.join(self.working_dir, f"{self.namespace}.graphml")
        preloaded_graph = self.load_nx_graph(self._graphml_xml_file)
        if preloaded_graph is not None:
            logger.info(
                "[NetworkXHyperStorage] Loaded graph from %s with %d nodes, %d edges",
                self._graphml_xml_file,
                preloaded_graph.number_of_nodes(),
                preloaded_graph.number_of_edges(),
            )
        # Use DiGraph to preserve direction (hyperedge -> entity)
        self._graph: nx.DiGraph = preloaded_graph or nx.DiGraph()

    # ---------- static helpers ----------
    @staticmethod
    def load_nx_graph(file_name: str) -> Optional[nx.DiGraph]:
        if os.path.exists(file_name):
            g = nx.read_graphml(file_name)
            # Ensure DiGraph
            if not isinstance(g, (nx.DiGraph, nx.MultiDiGraph)):
                g = nx.DiGraph(g)
            elif isinstance(g, nx.MultiDiGraph):
                g = nx.DiGraph(g)  # collapse parallel edges if any
            return g  # type: ignore[return-value]
        return None

    @staticmethod
    def write_nx_graph(graph: nx.DiGraph, file_name: str):
        logger.info(
            "[NetworkXHyperStorage] Writing graph with %d nodes, %d edges",
            graph.number_of_nodes(),
            graph.number_of_edges(),
        )
        nx.write_graphml(graph, file_name)

    # ---------- GraphStorage API ----------
    async def index_done_callback(self):
        self.write_nx_graph(self._graph, self._graphml_xml_file)

    async def has_node(self, node_id: str) -> bool:
        return self._graph.has_node(node_id)

    async def has_edge(self, source_node_id: str, target_node_id: str) -> bool:
        return self._graph.has_edge(source_node_id, target_node_id)

    async def node_degree(self, node_id: str) -> int:
        # total degree = in + out for DiGraph
        if not self._graph.has_node(node_id):
            return 0
        return int(self._graph.in_degree(node_id) + self._graph.out_degree(node_id))

    async def edge_degree(self, src_id: str, tgt_id: str) -> int:
        return await self.node_degree(src_id) + await self.node_degree(tgt_id)

    async def get_node(self, node_id: str) -> Union[dict, None]:
        if not self._graph.has_node(node_id):
            return None
        return dict(self._graph.nodes[node_id])

    async def get_edge(self, source_node_id: str, target_node_id: str) -> Union[dict, None]:
        if not self._graph.has_edge(source_node_id, target_node_id):
            return None
        return dict(self._graph.edges[(source_node_id, target_node_id)])

    async def get_node_edges(self, source_node_id: str) -> Union[list[tuple[str, str]], None]:
        if not self._graph.has_node(source_node_id):
            return None
        out_edges: Iterable[tuple[str, str]] = self._graph.out_edges(source_node_id)
        return [(u, v) for (u, v) in out_edges]

    async def upsert_node(self, node_id: str, node_data: dict):
        # Add or update node with attributes
        if self._graph.has_node(node_id):
            self._graph.nodes[node_id].update(node_data)
        else:
            self._graph.add_node(node_id, **node_data)

    async def upsert_edge(self, source_node_id: str, target_node_id: str, edge_data: dict):
        # Ensure nodes exist before adding the edge
        if not self._graph.has_node(source_node_id):
            self._graph.add_node(source_node_id)
        if not self._graph.has_node(target_node_id):
            self._graph.add_node(target_node_id)
        if self._graph.has_edge(source_node_id, target_node_id):
            self._graph.edges[(source_node_id, target_node_id)].update(edge_data)
        else:
            self._graph.add_edge(source_node_id, target_node_id, **edge_data)

    # ---------- convenience helpers (optional) ----------
    async def get_all_nodes(self) -> list[tuple[str, dict]]:
        return [(n, dict(d)) for n, d in self._graph.nodes(data=True)]

    async def get_all_edges(self) -> list[tuple[str, str, dict]]:
        return [(u, v, dict(d)) for u, v, d in self._graph.edges(data=True)]

